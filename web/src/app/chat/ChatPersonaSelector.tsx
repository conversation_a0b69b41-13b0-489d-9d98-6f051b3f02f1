import { Persona } from "@/app/admin/assistants/interfaces";
import { FiC<PERSON>ck, FiChevronDown, FiPlusSquare, FiEdit2 } from "react-icons/fi";
import React from "react"; // Re-add React import
import { CustomDropdown, DefaultDropdownElement } from "@/components/Dropdown";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { checkUserIdOwnsAssistant } from "@/lib/assistants/checkOwnership";
import { SEARCH_PARAM_NAMES } from "./searchParams";

function PersonaItem({
  id,
  name,
  onSelect,
  isSelected,
  isOwner,
}: {
  id: number;
  name: string;
  onSelect: (personaId: number) => void;
  isSelected: boolean;
  isOwner: boolean;
}) {
  return (
    <div className="flex w-full">
      <div
        className={`
          flex
          flex-grow
          px-3 
          text-sm 
          py-2 
          my-0.5
          rounded
          mx-1
          select-none 
          cursor-pointer 
          text-text-darker
          bg-background
          hover:bg-accent-background
          ${
            isSelected
              ? "bg-accent-background-hovered text-selected-emphasis"
              : ""
          }
        `}
        onClick={() => {
          onSelect(id);
        }}
      >
        {name}
        {isSelected && (
          <div className="ml-auto mr-1 my-auto">
            <FiCheck />
          </div>
        )}
      </div>
      {isOwner && (
        <Link href={`/assistants/edit/${id}`} className="mx-2 my-auto">
          <FiEdit2
            className="hover:bg-accent-background-hovered p-0.5 my-auto"
            size={20}
          />
        </Link>
      )}
    </div>
  );
}

export function ChatPersonaSelector({
  personas,
  selectedPersonaId,
  onPersonaChange,
  userId,
}: {
  personas: Persona[];
  selectedPersonaId: number | null;
  onPersonaChange: (persona: Persona | null) => void;
  userId: string | undefined;
}) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const currentlySelectedPersona = personas.find(
    (persona) => persona.id === selectedPersonaId
  );

  const updatePersonaInUrl = (personaId: number | null) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());
    if (personaId !== null) {
      newSearchParams.set(SEARCH_PARAM_NAMES.PERSONA_ID, personaId.toString());
    } else {
      newSearchParams.delete(SEARCH_PARAM_NAMES.PERSONA_ID);
    }
    router.replace(`?${newSearchParams.toString()}`, { scroll: false });
  };

  return (
    <CustomDropdown
      dropdown={
        <div
          className={`
            border 
            border-border 
            bg-background
            rounded-lg 
            shadow-lg 
            flex 
            flex-col 
            w-64 
            max-h-96 
            overflow-y-auto 
            p-1
            overscroll-contain`}
        >
          {personas.map((persona) => {
            const isSelected = persona.id === selectedPersonaId;
            const isOwner = checkUserIdOwnsAssistant(userId, persona);
            return (
              <PersonaItem
                key={persona.id}
                id={persona.id}
                name={persona.name}
                onSelect={(clickedPersonaId) => {
                  const clickedPersona = personas.find(
                    (persona) => persona.id === clickedPersonaId
                  );
                  if (clickedPersona) {
                    onPersonaChange(clickedPersona);
                    updatePersonaInUrl(clickedPersona.id);
                  }
                }}
                isSelected={isSelected}
                isOwner={isOwner}
              />
            );
          })}

          <DefaultDropdownElement
            name={
              <div className="flex items-center">
                <FiCheck className="mr-2" />
                Default Assistant
              </div>
            }
            onSelect={() => {
              onPersonaChange(null);
              updatePersonaInUrl(null);
            }}
            isSelected={selectedPersonaId === null}
          />

          <div className="border-t border-border pt-2">
            <DefaultDropdownElement
              name={
                <div className="flex items-center">
                  <FiPlusSquare className="mr-2" />
                  New Assistant
                </div>
              }
              onSelect={() => router.push("/assistants/new")}
              isSelected={false}
            />
          </div>
        </div>
      }
    >
      <div className="select-none text-xl text-strong font-bold flex px-2 rounded cursor-pointer hover:bg-accent-background">
        <div className="mt-auto">
          {currentlySelectedPersona?.name || "Default"}
        </div>
        <FiChevronDown className="my-auto ml-1" />
      </div>
    </CustomDropdown>
  );
}
