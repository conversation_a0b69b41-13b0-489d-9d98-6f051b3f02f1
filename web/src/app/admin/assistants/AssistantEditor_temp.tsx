"use client";

import React from "react";
import { Option } from "@/components/Dropdown";
import { generateRandomIconShape } from "@/lib/assistantIconUtils";
import {
  ConnectorStatus,
  DocumentSet,
  User,
  UserRole,
} from "@/lib/types";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { ArrayHelpers, FieldArray, Form, Formik, FormikProps } from "formik";

import {
  BooleanFormField,
  Label,
  TextFormField,
} from "@/components/admin/connectors/Field";

import { usePopup } from "@/components/admin/connectors/Popup";
import { getDisplayNameForModel, useLabels } from "@/lib/hooks";
import { DocumentSetSelectable } from "@/components/documentSet/DocumentSetSelectable";
import { addAssistantToList } from "@/lib/assistants/updateAssistantPreferences";
import {
  checkLLMSupportsImageInput,
  destructureValue,
  structureValue,
} from "@/lib/llm/utils";
import { ToolSnapshot } from "@/lib/tools/interfaces";
import { checkUserIsNoAuthUser } from "@/lib/user";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import * as Yup from "yup";
import CollapsibleSection from "./CollapsibleSection";
import { SuccessfulPersonaUpdateRedirectType } from "./enums";
import { Persona, PersonaLabel, StarterMessage } from "./interfaces";
import {
  PersonaUpsertParameters,
  createPersona,
  updatePersona,
  deletePersona,
} from "./lib";
import {
  CameraIcon,
  NewChatIcon,
  SwapIcon,
  TrashIcon,
} from "@/components/icons/icons";
import { buildImgUrl } from "@/app/chat/files/images/utils";
import { useAssistants } from "@/components/context/AssistantsContext";
import { debounce } from "lodash";
import { FullLLMProvider } from "../configuration/llm/interfaces";
import StarterMessagesList from "./StarterMessageList";

import { SwitchField } from "@/components/ui/switch";
import { generateIdenticon } from "@/components/assistants/AssistantIcon";
import { BackButton } from "@/components/BackButton";
import { AdvancedOptionsToggle } from "@/components/AdvancedOptionsToggle";
import { MinimalUserSnapshot } from "@/lib/types";
import { useUserGroups } from "@/lib/hooks";
import { useUserTeams } from "@/lib/hooks";
import { LLMSelector } from "@/components/llm/LLMSelector";
import useSWR from "swr";
import { errorHandlingFetcher } from "@/lib/fetcher";
import { ConfirmEntityModal } from "@/components/modals/ConfirmEntityModal";
import { SEARCH_TOOL_ID } from "@/app/chat/tools/constants";
import { ScrollArea } from "@/components/ui/scroll-area"; // adjust import path as needed
import { PrivacyToggle } from "@/components/PrivacyToggle";
function findSearchTool(tools: ToolSnapshot[]) {
  return tools.find((tool) => tool.in_code_tool_id === SEARCH_TOOL_ID);
}

function findImageGenerationTool(tools: ToolSnapshot[]) {
  return tools.find((tool) => tool.in_code_tool_id === "ImageGenerationTool");
}

function findInternetSearchTool(tools: ToolSnapshot[]) {
  return tools.find((tool) => tool.in_code_tool_id === "InternetSearchTool");
}

function SubLabel({ children }: { children: string | JSX.Element }) {
  return (
    <div
      className="text-sm text-description font-description mb-2"
      style={{ color: "rgb(113, 114, 121)" }}
    >
      {children}
    </div>
  );
}

// Add UserTeam type for local use
interface UserTeam {
  id: number;
  name: string;
}

// Helper function to check if user role should have Access Control restrictions
function isRestrictedRole(userRole: UserRole | undefined): boolean {
  return userRole === UserRole.TEAM_ADMIN || userRole === UserRole.BASIC;
}

export function AssistantEditor({
  existingPersona,
  ccPairs,
  documentSets,
  user,
  defaultPublic,
  llmProviders,
  tools,
  shouldAddAssistantToUserPreferences,
  admin,
}: {
  existingPersona?: Persona | null;
  ccPairs: ConnectorStatus<any, any>[];
  documentSets: DocumentSet[];
  user: User | null;
  defaultPublic: boolean;
  llmProviders: FullLLMProvider[];
  tools: ToolSnapshot[];
  shouldAddAssistantToUserPreferences?: boolean;
  admin?: boolean;
}) {
  const { refreshAssistants, isImageGenerationAvailable } = useAssistants();
  const router = useRouter();
  const searchParams = useSearchParams();
  const isAdminPage = searchParams.get("admin") === "true";
  const [searchTerm, setSearchTerm] = useState("");
  const { popup, setPopup } = usePopup();
  // const { labels, refreshLabels, createLabel, updateLabel, deleteLabel } =
  //   useLabels();

  const colorOptions = [
    "#FF6FBF",
    "#6FB1FF",
    "#B76FFF",
    "#FFB56F",
    "#6FFF8D",
    "#FF6F6F",
    "#6FFFFF",
  ];

  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // state to persist across formik reformatting
  const [defautIconColor, _setDeafultIconColor] = useState(
    colorOptions[Math.floor(Math.random() * colorOptions.length)]
  );
  const [isRefreshing, setIsRefreshing] = useState(false);

  const [defaultIconShape, setDefaultIconShape] = useState<any>(null);

  useEffect(() => {
    if (defaultIconShape === null) {
      setDefaultIconShape(generateRandomIconShape().encodedGrid);
    }
  }, [defaultIconShape]);

  const [removePersonaImage, setRemovePersonaImage] = useState(false);

  const autoStarterMessageEnabled = useMemo(
    () => llmProviders.length > 0,
    [llmProviders.length]
  );
  const isUpdate = existingPersona !== undefined && existingPersona !== null;
  const existingPrompt = existingPersona?.prompts[0] ?? null;
  // Since is_default_provider was removed in favor of team-specific defaults,
  // use the first available provider as fallback
  const defaultProvider = llmProviders.length > 0 ? llmProviders[0] : null;
  const defaultModelName = defaultProvider?.default_model_name;
  const providerDisplayNameToProviderName = new Map<string, string>();
  llmProviders.forEach((llmProvider) => {
    providerDisplayNameToProviderName.set(
      llmProvider.name,
      llmProvider.provider
    );
  });

  const modelOptionsByProvider = new Map<string, Option<string>[]>();
  llmProviders.forEach((llmProvider) => {
    const providerOptions = llmProvider.model_names.map((modelName) => {
      return {
        name: getDisplayNameForModel(modelName),
        value: modelName,
      };
    });
    modelOptionsByProvider.set(llmProvider.name, providerOptions);
  });

  const personaCurrentToolIds =
    existingPersona?.tools.map((tool) => tool.id) || [];

  const searchTool = findSearchTool(tools);
  const imageGenerationTool = findImageGenerationTool(tools);
  const internetSearchTool = findInternetSearchTool(tools);

  const customTools = tools.filter(
    (tool) =>
      tool.in_code_tool_id !== searchTool?.in_code_tool_id &&
      tool.in_code_tool_id !== imageGenerationTool?.in_code_tool_id &&
      tool.in_code_tool_id !== internetSearchTool?.in_code_tool_id
  );

  const availableTools = [
    ...customTools,
    ...(searchTool ? [searchTool] : []),
    ...(imageGenerationTool ? [imageGenerationTool] : []),
    ...(internetSearchTool ? [internetSearchTool] : []),
  ];
  const enabledToolsMap: { [key: number]: boolean } = {};
  availableTools.forEach((tool) => {
    enabledToolsMap[tool.id] = personaCurrentToolIds.includes(tool.id);
  });

  /**
   * Filter tools based on assistant privacy settings and selected teams
   * - Public assistants: Show all tools (built-in tools are always available)
   * - Private assistants: Show built-in tools + public tools + private tools assigned to selected teams
   */
  const getFilteredTools = (tools: ToolSnapshot[], isPublic: boolean, selectedTeams: number[]) => {
    if (isPublic || selectedTeams.length === 0) {
      return tools;
    }

    // Filter tools to only show those that belong to the selected teams
    return tools.filter(tool => {
      // Built-in tools (search, image generation, internet search) are always available
      if (tool.in_code_tool_id === SEARCH_TOOL_ID ||
          tool.in_code_tool_id === "ImageGenerationTool" ||
          tool.in_code_tool_id === "InternetSearchTool") {
        return true;
      }

      // For custom tools, check if they belong to any of the selected teams
      // Tools have a user_teams property that contains team IDs
      return (tool.user_teams && tool.user_teams.some(teamId => selectedTeams.includes(teamId)));
    });
  };

  /**
   * Filter LLM providers based on assistant privacy settings and selected teams
   * - Public assistants: Return empty array (forces "User Default" only)
   * - Private assistants: Show public LLM providers + private LLM providers assigned to selected teams
   */
  const getFilteredLLMProviders = (providers: FullLLMProvider[], isPublic: boolean, selectedTeams: number[]) => {
    // For public assistants, return empty array to force "User Default" only
    if (isPublic) {
      return [];
    }

    if (selectedTeams.length === 0) {
      return providers;
    }

    // Filter LLM providers to only show public ones and those that belong to the selected teams
    return providers.filter(provider => {
      return provider.is_public ||
             (provider.user_teams && provider.user_teams.some(teamId => selectedTeams.includes(teamId)));
    });
  };

  /**
   * Filter document sets based on assistant privacy settings, selected teams, and user role
   * - ADMIN users with public assistants: Show ONLY public document sets
   * - ADMIN users with private assistants: Show public + private document sets assigned to selected teams
   * - TEAM_ADMIN users (both public and private assistants): Show ONLY private document sets assigned to their teams (NO public ones)
   */
  const getFilteredDocumentSets = (documentSets: DocumentSet[], isPublic: boolean, selectedTeams: number[]) => {
    // Special handling for TEAM_ADMIN users
    if (user?.role === UserRole.TEAM_ADMIN) {
      const currentUserTeamIds = userTeams?.map(team => team.id) || [];

      if (isPublic) {
        // For public assistants created by team admins: show ONLY their team's private document sets (NOT public ones)
        // This matches the requirement: "team admins in assistant creation also in knowledge base they should not see public document set"
        return documentSets.filter(documentSet => {
          return !documentSet.is_public &&
                 documentSet.user_teams &&
                 documentSet.user_teams.some(teamId => currentUserTeamIds.includes(teamId));
        });
      } else {
        // For private assistants created by team admins: show ONLY their team's private document sets (NOT public ones)
        // Team admins should never see public document sets
        return documentSets.filter(documentSet => {
          return !documentSet.is_public &&
                 documentSet.user_teams &&
                 documentSet.user_teams.some(teamId => currentUserTeamIds.includes(teamId));
        });
      }
    }

    // For ADMIN users
    if (isPublic) {
      // For public assistants: only show public document sets
      // This ensures public assistants can only access public knowledge
      return documentSets.filter(documentSet => documentSet.is_public);
    }

    if (selectedTeams.length === 0) {
      // For private assistants with no teams selected: show all document sets (fallback)
      return documentSets;
    }

    // For private assistants with teams selected: show public document sets + private document sets assigned to selected teams
    return documentSets.filter(documentSet => {
      return documentSet.is_public ||
             (documentSet.user_teams && documentSet.user_teams.some(teamId => selectedTeams.includes(teamId)));
    });
  };

  const [showVisibilityWarning, setShowVisibilityWarning] = useState(false);

  // Check if current user has restricted role
  const userHasRestrictedRole = isRestrictedRole(user?.role);

  const initialValues = {
    name: existingPersona?.name ?? "",
    description: existingPersona?.description ?? "",
    datetime_aware: existingPrompt?.datetime_aware ?? false,
    system_prompt: existingPrompt?.system_prompt ?? "",
    task_prompt: existingPrompt?.task_prompt ?? "",
    is_public: userHasRestrictedRole ? false : (existingPersona?.is_public ?? defaultPublic),
    document_set_ids:
      existingPersona?.document_sets?.map((documentSet) => documentSet.id) ??
      ([] as number[]),
    num_chunks: existingPersona?.num_chunks ?? null,
    search_start_date: existingPersona?.search_start_date
      ? existingPersona?.search_start_date.toString().split("T")[0]
      : null,
    include_citations: existingPersona?.prompts[0]?.include_citations ?? true,
    llm_relevance_filter: existingPersona?.llm_relevance_filter ?? false,
    llm_model_provider_override:
      existingPersona?.llm_model_provider_override ?? null,
    llm_model_version_override:
      existingPersona?.llm_model_version_override ?? null,
    starter_messages: existingPersona?.starter_messages?.length
      ? existingPersona.starter_messages
      : [{ message: "" }],
    enabled_tools_map: enabledToolsMap,
    icon_color: existingPersona?.icon_color ?? defautIconColor,
    icon_shape: existingPersona?.icon_shape ?? defaultIconShape,
    uploaded_image: null,
    display_sources: existingPersona?.display_sources ?? true,
    // labels: existingPersona?.labels ?? null,

    // EE Only
    label_ids: existingPersona?.labels?.map((label) => label.id) ?? [],
    selectedUsers: userHasRestrictedRole ? [] : (
      existingPersona?.users?.filter(
        (u) => u.id !== existingPersona.owner?.id
      ) ?? []
    ),
    selectedGroups: userHasRestrictedRole ? [] : (existingPersona?.groups ?? []),
    selectedTeams: userHasRestrictedRole ? [] : (existingPersona?.user_teams ?? []),
    is_default_persona: existingPersona?.is_default_persona ?? false,
    available_llm_models: existingPersona?.available_llm_models ?? [],
  };

  interface AssistantPrompt {
    message: string;
    name: string;
  }

  const debouncedRefreshPrompts = debounce(
    async (formValues: any, setFieldValue: any) => {
      if (!autoStarterMessageEnabled) {
        return;
      }
      setIsRefreshing(true);
      try {
        const response = await fetch("/api/persona/assistant-prompt-refresh", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: formValues.name || "",
            description: formValues.description || "",
            document_set_ids: formValues.document_set_ids || [],
            instructions:
              formValues.system_prompt || formValues.task_prompt || "",
            generation_count:
              4 -
              formValues.starter_messages.filter(
                (message: StarterMessage) => message.message.trim() !== ""
              ).length,
          }),
        });

        const data: AssistantPrompt[] = await response.json();
        if (response.ok) {
          const filteredStarterMessages = formValues.starter_messages.filter(
            (message: StarterMessage) => message.message.trim() !== ""
          );
          setFieldValue("starter_messages", [
            ...filteredStarterMessages,
            ...data,
          ]);
        }
      } catch (error) {
        console.error("Failed to refresh prompts:", error);
      } finally {
        setIsRefreshing(false);
      }
    },
    1000
  );

  const [labelToDelete, setLabelToDelete] = useState<PersonaLabel | null>(null);
  const [isRequestSuccessful, setIsRequestSuccessful] = useState(false);

  const { data: userGroups } = useUserGroups();
  const { data: userTeams } = useUserTeams();

  const { data: users } = useSWR<MinimalUserSnapshot[]>(
    "/api/users",
    errorHandlingFetcher
  );

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  // if (!labels) {
  //   return <></>;
  // }

  const openDeleteModal = () => {
    setDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
  };

  const handleDeletePersona = async () => {
    if (existingPersona) {
      const response = await deletePersona(existingPersona.id);
      if (response.ok) {
        await refreshAssistants();
        router.push(
          isAdminPage ? `/admin/assistants?u=${Date.now()}` : `/chat`
        );
      } else {
        setPopup({
          type: "error",
          message: `Failed to delete persona - ${await response.text()}`,
        });
      }
    }
  };

  return (
    <div className="mx-auto max-w-4xl">
      <style>
        {`
          .assistant-editor input::placeholder,
          .assistant-editor textarea::placeholder {
            opacity: 0.5;
          }
        `}
      </style>
      {!admin && (
        <div className="absolute top-4 left-4">
          <BackButton />
        </div>
      )}

      {/* {labelToDelete && (
        <ConfirmEntityModal
          entityType="label"
          entityName={labelToDelete.name}
          onClose={() => setLabelToDelete(null)}
          onSubmit={async () => {
            const response = await deleteLabel(labelToDelete.id);
            if (response?.ok) {
              setPopup({
                message: `Label deleted successfully`,
                type: "success",
              });
              await refreshLabels();
            } else {
              setPopup({
                message: `Failed to delete label - ${await response.text()}`,
                type: "error",
              });
            }
            setLabelToDelete(null);
          }}
        />
      )} */}
      {deleteModalOpen && existingPersona && (
        <ConfirmEntityModal
          entityType="Persona"
          entityName={existingPersona.name}
          onClose={closeDeleteModal}
          onSubmit={handleDeletePersona}
        />
      )}
      {popup}
      <Formik
        enableReinitialize={true}
        initialValues={initialValues}
        validationSchema={Yup.object()
          .shape({
            name: Yup.string().required(
              "Must provide a name for the Assistant").matches(/^[^\/\\]*$/, "Name cannot contain '/' or '\\'"),
            description: Yup.string().required(
              "Must provide a description for the Assistant"
            ),
            system_prompt: Yup.string(),
            task_prompt: Yup.string(),
            is_public: Yup.boolean().required(),
            document_set_ids: Yup.array().of(Yup.number()),
            num_chunks: Yup.number().nullable(),
            include_citations: Yup.boolean().required(),
            llm_relevance_filter: Yup.boolean().required(),
            display_sources: Yup.boolean().required(),
            llm_model_version_override: Yup.string().nullable(),
            llm_model_provider_override: Yup.string().nullable(),
            starter_messages: Yup.array().of(
              Yup.object().shape({
                message: Yup.string(),
              })
            ),
            search_start_date: Yup.date().nullable(),
            icon_color: Yup.string(),
            icon_shape: Yup.number(),
            uploaded_image: Yup.mixed().nullable(),
            // EE Only
            label_ids: Yup.array().of(Yup.number()),
            selectedUsers: Yup.array().of(Yup.object()),
            selectedGroups: Yup.array().of(Yup.number()),
            selectedTeams: Yup.array().of(Yup.number()),
            is_default_persona: Yup.boolean().required(),
            available_llm_models: Yup.array().of(Yup.string()),
          })
          .test(
            "system-prompt-or-task-prompt",
            "Must provide either Instructions or Reminders (Advanced)",
            function (values) {
              const systemPromptSpecified =
                values.system_prompt && values.system_prompt.trim().length > 0;
              const taskPromptSpecified =
                values.task_prompt && values.task_prompt.trim().length > 0;

              if (systemPromptSpecified || taskPromptSpecified) {
                return true;
              }

              return this.createError({
                path: "system_prompt",
                message:
                  "Must provide either Instructions or Reminders (Advanced)",
              });
            }
          )
          .test(
            "default-persona-public",
            "Default persona must be public",
            function (values) {
              if (values.is_default_persona && !values.is_public) {
                return this.createError({
                  path: "is_public",
                  message: "Default persona must be public",
                });
              }
              return true;
            }
          )}
        onSubmit={async (values, formikHelpers) => {
          if (
            values.llm_model_provider_override &&
            !values.llm_model_version_override
          ) {
            setPopup({
              type: "error",
              message:
                "Must select a model if a non-default LLM provider is chosen.",
            });
            return;
          }
          // Allow "User Default" (null values) - backend will resolve team default
          // Only require explicit selection if user has manually chosen a provider but not a model
          if (values.llm_model_provider_override && !values.llm_model_version_override) {
            setPopup({
              type: "error",
              message: "Must select a model if a non-default LLM provider is chosen.",
            });
            return;
          }

          formikHelpers.setSubmitting(true);
          let enabledTools = Object.keys(values.enabled_tools_map)
            .map((toolId) => Number(toolId))
            .filter((toolId) => values.enabled_tools_map[toolId]);

          const searchToolEnabled = searchTool
            ? enabledTools.includes(searchTool.id)
            : false;

          // if disable_retrieval is set, set num_chunks to 0
          // to tell the backend to not fetch any documents
          const numChunks = searchToolEnabled ? values.num_chunks || 10 : 0;
          const starterMessages = values.starter_messages
            .filter(
              (message: { message: string }) => message.message.trim() !== ""
            )
            .map((message: { message: string; name?: string }) => ({
              message: message.message,
              name: message.message,
            }));

          // don't set groups if marked as public
          const groups = values.is_public ? [] : values.selectedGroups;

          // For restricted roles, override certain values
          const finalIsPublic = userHasRestrictedRole ? false : values.is_public;
          const finalSelectedUsers = userHasRestrictedRole ? [] : values.selectedUsers;
          const finalSelectedGroups = userHasRestrictedRole ? [] : values.selectedGroups;

          // For team admins, send empty array - backend validation now handles team admins properly
          // Backend will auto-populate with user's teams after validation passes
          const finalSelectedTeams = userHasRestrictedRole
            ? []  // Empty array for team admins - backend auto-populates
            : values.selectedTeams;

          const submissionData: PersonaUpsertParameters = {
            ...values,
            is_public: finalIsPublic,
            existing_prompt_id: existingPrompt?.id ?? null,
            starter_messages: starterMessages,
            groups: finalIsPublic ? [] : finalSelectedGroups,
            users: finalIsPublic
              ? undefined
              : [
                  ...(user && !checkUserIsNoAuthUser(user.id) ? [user.id] : []),
                  ...finalSelectedUsers.map((u: MinimalUserSnapshot) => u.id),
                ],
            user_teams: finalSelectedTeams,
            tool_ids: enabledTools,
            remove_image: removePersonaImage,
            search_start_date: values.search_start_date
              ? new Date(values.search_start_date)
              : null,
            num_chunks: numChunks,
          };

          let personaResponse;
          if (isUpdate) {
            personaResponse = await updatePersona(
              existingPersona.id,
              submissionData
            );
          } else {
            personaResponse = await createPersona(submissionData);
          }

          let error = null;

          if (!personaResponse) {
            error = "Failed to create Assistant - no response received";
          } else if (!personaResponse.ok) {
            error = await personaResponse.text();
          }

          if (error || !personaResponse) {
            setPopup({
              type: "error",
              message: `Failed to create Assistant - ${error}`,
            });
            formikHelpers.setSubmitting(false);
          } else {
            const assistant = await personaResponse.json();
            const assistantId = assistant.id;
            if (
              shouldAddAssistantToUserPreferences &&
              user?.preferences?.chosen_assistants
            ) {
              const success = await addAssistantToList(assistantId);
              if (success) {
                setPopup({
                  message: `"${assistant.name}" has been added to your list.`,
                  type: "success",
                });
                await refreshAssistants();
              } else {
                setPopup({
                  message: `"${assistant.name}" could not be added to your list.`,
                  type: "error",
                });
              }
            }

            await refreshAssistants();

            router.push(
              isAdminPage
                ? `/admin/assistants?u=${Date.now()}`
                : `/chat?assistantId=${assistantId}`
            );
            setIsRequestSuccessful(true);
          }
        }}
      >
        {({
          isSubmitting,
          values,
          setFieldValue,
          errors,
          ...formikProps
        }: FormikProps<any>) => {
          function toggleToolInValues(toolId: number) {
            const updatedEnabledToolsMap = {
              ...values.enabled_tools_map,
              [toolId]: !values.enabled_tools_map[toolId],
            };
            setFieldValue("enabled_tools_map", updatedEnabledToolsMap);
          }

          // model must support image input for image generation
          // to work
          const currentLLMSupportsImageOutput = checkLLMSupportsImageInput(
            values.llm_model_version_override || defaultModelName || ""
          );

          // Get filtered tools, LLM providers, and document sets based on privacy settings
          const filteredTools = getFilteredTools(availableTools, values.is_public, values.selectedTeams || []);
          const filteredLLMProviders = getFilteredLLMProviders(llmProviders, values.is_public, values.selectedTeams || []);
          const filteredDocumentSets = getFilteredDocumentSets(documentSets, values.is_public, values.selectedTeams || []);

          // Separate the filtered tools into categories
          const filteredSearchTool = filteredTools.find((tool) => tool.in_code_tool_id === SEARCH_TOOL_ID);
          const filteredImageGenerationTool = filteredTools.find((tool) => tool.in_code_tool_id === "ImageGenerationTool");
          const filteredInternetSearchTool = filteredTools.find((tool) => tool.in_code_tool_id === "InternetSearchTool");
          const filteredCustomTools = filteredTools.filter(
            (tool) =>
              tool.in_code_tool_id !== SEARCH_TOOL_ID &&
              tool.in_code_tool_id !== "ImageGenerationTool" &&
              tool.in_code_tool_id !== "InternetSearchTool"
          );

          return (
            <Form className="w-full text-text-950 assistant-editor">
              {/* Refresh starter messages when name or description changes */}
              <p className="text-base font-normal text-2xl">
                {existingPersona ? (
                  <>
                    Edit assistant <b>{existingPersona.name}</b>
                  </>
                ) : (
                  "Create an Assistant"
                )}
              </p>
              <div className="max-w-4xl w-full">
                <Separator />
                <div className="flex gap-x-2 items-center">
                  <div className="block font-medium text-sm">
                    Assistant Icon
                  </div>
                </div>
                <SubLabel>
                  The icon that will visually represent your Assistant
                </SubLabel>
                <div className="flex gap-x-2 items-center">
                  <div
                    className="p-4 cursor-pointer  rounded-full flex  "
                    style={{
                      borderStyle: "dashed",
                      borderWidth: "1.5px",
                      borderSpacing: "4px",
                    }}
                  >
                    {values.uploaded_image ? (
                      <img
                        src={URL.createObjectURL(values.uploaded_image)}
                        alt="Uploaded assistant icon"
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : existingPersona?.uploaded_image_id &&
                      !removePersonaImage ? (
                      <img
                        src={buildImgUrl(existingPersona?.uploaded_image_id)}
                        alt="Uploaded assistant icon"
